// components/StoreSearch.tsx
import React, { useState, useCallback, useEffect } from "react";
import TopBarLayout from "../../components/TopBarLayout";
import TableComponent from "../../components/TableComponent";
import { Box } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import { useNavigate } from "react-router-dom";
import { setSearchQuery, setStoreFilters, resetStoreFilters } from "../../store/slice/storeSlice";
import { getSearchStores } from "../../services/action/store.action";
import { ThunkDispatch } from "redux-thunk";
import { UnknownAction } from "@reduxjs/toolkit";
import FilterRow from "../../components/FilterRow";
import { debounce } from "@mui/material/utils";

type AppDispatch = ThunkDispatch<RootState, unknown, UnknownAction>;

const StoreSearch = () => {
  const dispatch: AppDispatch = useDispatch();
  const navigate = useNavigate();
  const { stores, isLoading, filters, generic_search, totalCount } = useSelector(
    (state: RootState) => state.store,
  );

  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });

  const [isFilterApplied, setIsFilterApplied] = useState(false);

  // Reset filters when component mounts
  useEffect(() => {
    dispatch(resetStoreFilters());
    dispatch(setSearchQuery(""));
  }, [dispatch]);

  const fetchStores = useCallback(() => {
    dispatch(
      getSearchStores({
        limit: pagination.rowsPerPage,
        page: pagination.page,
        generic_search,
        ...filters,
      }),
    );
  }, [dispatch, filters, generic_search, pagination.page, pagination.rowsPerPage]);

  const handleSearch = useCallback(
    debounce((query: string) => {
      dispatch(setSearchQuery(query));
      setPagination((prev) => ({ ...prev, page: 1 }));
      setIsFilterApplied(true);
    }, 500),
    [dispatch],
  );

  const handleFilterChange = (name: string, value: string) => {
    dispatch(setStoreFilters({ [name]: value }));
  };

  const handleApplyFilters = () => {
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(true);
    // Explicitly call API when user applies filter
    dispatch(
      getSearchStores({
        limit: pagination.rowsPerPage,
        page: 1,
        generic_search,
        ...filters,
      }),
    );
  };

  const handleResetFilters = () => {
    dispatch(resetStoreFilters());
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(false);
  };

  const handleNutroIDClick = (nutroStoreID: string, storeName: string) => {
    navigate(`/store-search/${nutroStoreID}`, { state: { storeName } });
  };

  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });

    if (isFilterApplied) {
      dispatch(
        getSearchStores({
          limit: newRowsPerPage,
          page: newPage,
          generic_search,
          ...filters,
        }),
      );
    }
  };

  const columns = [
    {
      id: "NutroStoreID",
      label: "Nutro ID",
      description: "Unique identifier for the store",
      format: (value: string, row: any) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            textDecoration: "underline",
            cursor: "pointer",
            "&:hover": {
              color: "primary.dark",
              fontWeight: "500",
            },
          }}
          onClick={() => handleNutroIDClick(value, row.StoreName)}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "StoreName",
      label: "Store Name",
      description: "Name of the store location",
    },
    {
      id: "Address",
      label: "Address",
      description: "Street address of the store",
    },
    {
      id: "City",
      label: "City",
      description: "City where the store is located",
    },
    {
      id: "State",
      label: "State",
      description: "State where the store is located",
    },
    {
      id: "ZIP",
      label: "ZIP",
      description: "Postal code of the store location",
    },
    {
      id: "Phone",
      label: "Phone",
      description: "Contact phone number for the store",
    },
    {
      id: "Status",
      label: "Status",
      description: "Current operational status of the store",
      format: (value: string) => (
        <Box
          sx={{
            color: value === "Active" ? "success.main" : "error.main",
            fontWeight: "bold",
          }}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "TM",
      label: "TM",
      description: "Territory manager associated with the store",
    },
  ];

  const filterConfigs = [
    {
      name: "StoreName",
      label: "Store Name",
      type: "text" as const,
      value: filters.StoreName,
      onChange: (value: string) => handleFilterChange("StoreName", value),
    },
    {
      name: "NutroStoreID",
      label: "Nutro ID",
      type: "text" as const,
      value: filters.NutroStoreID,
      onChange: (value: string) => handleFilterChange("NutroStoreID", value),
    },
    {
      name: "Address",
      label: "Address",
      type: "text" as const,
      value: filters.Address,
      onChange: (value: string) => handleFilterChange("Address", value),
    },
    {
      name: "City",
      label: "City",
      type: "text" as const,
      value: filters.City,
      onChange: (value: string) => handleFilterChange("City", value),
    },
    {
      name: "State",
      label: "State",
      type: "text" as const,
      value: filters.State,
      onChange: (value: string) => handleFilterChange("State", value),
    },
    {
      name: "ZipCode",
      label: "ZipCode",
      type: "text" as const,
      value: filters.ZipCode,
      onChange: (value: string) => handleFilterChange("ZipCode", value),
    },
  ];

  return (
    <div>
      <TopBarLayout breadcrumbItems={["Store", "Store Search"]} onSearchChange={handleSearch} />

      <FilterRow
        filters={filterConfigs}
        onReset={handleResetFilters}
        onApply={handleApplyFilters}
      />

      {isFilterApplied && (
        <TableComponent
          columns={columns}
          rows={stores}
          rowsPerPage={pagination.rowsPerPage}
          page={pagination.page}
          totalCount={totalCount}
          onPageChange={handlePageChange}
          showActions={false}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default StoreSearch;
