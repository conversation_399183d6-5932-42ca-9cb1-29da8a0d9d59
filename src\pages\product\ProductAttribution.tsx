// components/ProductAttribution.tsx
import React, { useState, useCallback, useEffect } from "react";
import TopBarLayout from "../../components/TopBarLayout";
import TableComponent from "../../components/TableComponent";
import { Box, Tabs, Tab, Badge, Chip } from "@mui/material";
import { useNavigate } from "react-router-dom";
import FilterRow from "../../components/FilterRow";
import { debounce } from "@mui/material/utils";

// Product Attribution interface
interface ProductAttribution {
  id: string;
  PRDNO: string;
  DESCP: string;
  StoreID: string;
  StoreName: string;
  ChannelID: string;
  ChannelName: string;
  Status: "Mapped" | "Unmapped" | "Pending";
  MappedDate: string;
  MappedBy: string;
  Priority: "High" | "Medium" | "Low";
}

// Mock data for product attributions
const mockAttributions: ProductAttribution[] = [
  {
    id: "1",
    PRDNO: "PRD001",
    DESCP: "Premium Dog Food - Chicken & Rice",
    StoreID: "STR001",
    StoreName: "PetSmart Downtown",
    ChannelID: "CH001",
    ChannelName: "Retail",
    Status: "Mapped",
    MappedDate: "2024-01-15",
    MappedBy: "John Doe",
    Priority: "High"
  },
  {
    id: "2",
    PRDNO: "PRD002",
    DESCP: "Cat Food - Salmon & Tuna",
    StoreID: "STR002",
    StoreName: "Petco Mall",
    ChannelID: "CH001",
    ChannelName: "Retail",
    Status: "Pending",
    MappedDate: "2024-01-14",
    MappedBy: "Jane Smith",
    Priority: "Medium"
  },
  {
    id: "3",
    PRDNO: "PRD003",
    DESCP: "Puppy Training Treats",
    StoreID: "STR003",
    StoreName: "Pet Supplies Plus",
    ChannelID: "CH002",
    ChannelName: "Online",
    Status: "Unmapped",
    MappedDate: "2024-01-13",
    MappedBy: "Mike Johnson",
    Priority: "Low"
  },
  {
    id: "4",
    PRDNO: "PRD004",
    DESCP: "Senior Dog Food - Joint Care",
    StoreID: "STR004",
    StoreName: "Local Pet Store",
    ChannelID: "CH001",
    ChannelName: "Retail",
    Status: "Mapped",
    MappedDate: "2024-01-12",
    MappedBy: "Sarah Wilson",
    Priority: "High"
  },
  {
    id: "5",
    PRDNO: "PRD005",
    DESCP: "Kitten Food - Growth Formula",
    StoreID: "STR005",
    StoreName: "Pet Paradise",
    ChannelID: "CH002",
    ChannelName: "Online",
    Status: "Pending",
    MappedDate: "2024-01-11",
    MappedBy: "Tom Brown",
    Priority: "Medium"
  }
];

// Status options for dropdown
const statusOptions = [
  { value: "", label: "All Statuses" },
  { value: "Mapped", label: "Mapped" },
  { value: "Unmapped", label: "Unmapped" },
  { value: "Pending", label: "Pending" }
];

// Priority options for dropdown
const priorityOptions = [
  { value: "", label: "All Priorities" },
  { value: "High", label: "High" },
  { value: "Medium", label: "Medium" },
  { value: "Low", label: "Low" }
];

// Tab panel component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index} role="tabpanel">
    {value === index && <Box sx={{ paddingTop: '1.5rem' }}>{children}</Box>}
  </div>
);

const ProductAttributionPage = () => {
  const navigate = useNavigate();
  
  // Local state management
  const [attributions, setAttributions] = useState<ProductAttribution[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [genericSearch, setGenericSearch] = useState("");
  const [currentTab, setCurrentTab] = useState(0);
  
  // Filter state
  const [filters, setFilters] = useState({
    PRDNO: "",
    StoreID: "",
    Status: "",
    Priority: "",
    ChannelName: ""
  });

  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });

  const [isFilterApplied, setIsFilterApplied] = useState(false);

  // Reset filters when component mounts
  useEffect(() => {
    setFilters({
      PRDNO: "",
      StoreID: "",
      Status: "",
      Priority: "",
      ChannelName: ""
    });
    setGenericSearch("");
  }, []);

  // Mock API call simulation
  const fetchAttributions = useCallback(() => {
    setIsLoading(true);
    
    setTimeout(() => {
      let filteredAttributions = [...mockAttributions];
      
      // Apply tab filter
      if (currentTab === 1) {
        filteredAttributions = filteredAttributions.filter(attr => attr.Status === "Mapped");
      } else if (currentTab === 2) {
        filteredAttributions = filteredAttributions.filter(attr => attr.Status === "Unmapped");
      } else if (currentTab === 3) {
        filteredAttributions = filteredAttributions.filter(attr => attr.Status === "Pending");
      }
      
      // Apply generic search
      if (genericSearch) {
        filteredAttributions = filteredAttributions.filter(attribution =>
          Object.values(attribution).some(value =>
            value?.toString().toLowerCase().includes(genericSearch.toLowerCase())
          )
        );
      }
      
      // Apply specific filters
      if (filters.PRDNO) {
        filteredAttributions = filteredAttributions.filter(attr =>
          attr.PRDNO.toLowerCase().includes(filters.PRDNO.toLowerCase())
        );
      }
      
      if (filters.StoreID) {
        filteredAttributions = filteredAttributions.filter(attr =>
          attr.StoreID.toLowerCase().includes(filters.StoreID.toLowerCase())
        );
      }
      
      if (filters.Status) {
        filteredAttributions = filteredAttributions.filter(attr =>
          attr.Status === filters.Status
        );
      }
      
      if (filters.Priority) {
        filteredAttributions = filteredAttributions.filter(attr =>
          attr.Priority === filters.Priority
        );
      }
      
      if (filters.ChannelName) {
        filteredAttributions = filteredAttributions.filter(attr =>
          attr.ChannelName.toLowerCase().includes(filters.ChannelName.toLowerCase())
        );
      }
      
      // Apply pagination
      const startIndex = (pagination.page - 1) * pagination.rowsPerPage;
      const endIndex = startIndex + pagination.rowsPerPage;
      const paginatedAttributions = filteredAttributions.slice(startIndex, endIndex);
      
      setAttributions(paginatedAttributions);
      setTotalCount(filteredAttributions.length);
      setIsLoading(false);
    }, 500);
  }, [genericSearch, filters, pagination.page, pagination.rowsPerPage, currentTab]);

  // Count attributions by status for tab badges
  const statusCounts = {
    all: mockAttributions.length,
    mapped: mockAttributions.filter(attr => attr.Status === "Mapped").length,
    unmapped: mockAttributions.filter(attr => attr.Status === "Unmapped").length,
    pending: mockAttributions.filter(attr => attr.Status === "Pending").length,
  };

  // Event handlers
  const handleSearch = useCallback(
    debounce((query: string) => {
      setGenericSearch(query);
      setPagination((prev) => ({ ...prev, page: 1 }));
      setIsFilterApplied(true);
    }, 500),
    [],
  );

  const handleFilterChange = (name: string, value: string) => {
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const handleApplyFilters = () => {
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(true);
  };

  const handleResetFilters = () => {
    setFilters({
      PRDNO: "",
      StoreID: "",
      Status: "",
      Priority: "",
      ChannelName: ""
    });
    setGenericSearch("");
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(false);
    setAttributions([]);
    setTotalCount(0);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
    setPagination((prev) => ({ ...prev, page: 1 }));
    if (isFilterApplied) {
      // Trigger refetch when tab changes
      setTimeout(() => fetchAttributions(), 0);
    }
  };

  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  // Fetch attributions when filters are applied
  useEffect(() => {
    if (isFilterApplied) {
      fetchAttributions();
    }
  }, [fetchAttributions, isFilterApplied]);

  // Table columns
  const columns = [
    {
      id: "PRDNO",
      label: "Product No",
      description: "Product number identifier",
    },
    {
      id: "DESCP",
      label: "Description",
      description: "Product description",
    },
    {
      id: "StoreID",
      label: "Store ID",
      description: "Store identifier",
    },
    {
      id: "StoreName",
      label: "Store Name",
      description: "Name of the store",
    },
    {
      id: "ChannelName",
      label: "Channel",
      description: "Sales channel",
      format: (value: string) => (
        <Chip label={value} size="small" variant="outlined" />
      ),
    },
    {
      id: "Status",
      label: "Status",
      description: "Attribution status",
      format: (value: string) => (
        <Chip
          label={value}
          size="small"
          color={
            value === "Mapped" ? "success" :
            value === "Pending" ? "warning" :
            "error"
          }
        />
      ),
    },
    {
      id: "Priority",
      label: "Priority",
      description: "Attribution priority",
      format: (value: string) => (
        <Chip
          label={value}
          size="small"
          color={
            value === "High" ? "error" :
            value === "Medium" ? "warning" :
            "default"
          }
        />
      ),
    },
    {
      id: "MappedDate",
      label: "Mapped Date",
      description: "Date when attribution was mapped",
    },
  ];

  // Filter configurations
  const filterConfigs = [
    {
      name: "PRDNO",
      label: "Product No",
      type: "text" as const,
      value: filters.PRDNO,
      onChange: (value: string) => handleFilterChange("PRDNO", value),
    },
    {
      name: "StoreID",
      label: "Store ID",
      type: "text" as const,
      value: filters.StoreID,
      onChange: (value: string) => handleFilterChange("StoreID", value),
    },
    {
      name: "Status",
      label: "Status",
      type: "select" as const,
      value: filters.Status,
      onChange: (value: string) => handleFilterChange("Status", value),
      options: statusOptions,
    },
    {
      name: "Priority",
      label: "Priority",
      type: "select" as const,
      value: filters.Priority,
      onChange: (value: string) => handleFilterChange("Priority", value),
      options: priorityOptions,
    },
    {
      name: "ChannelName",
      label: "Channel",
      type: "text" as const,
      value: filters.ChannelName,
      onChange: (value: string) => handleFilterChange("ChannelName", value),
    },
  ];

  return (
    <div>
      {/* TopBarLayout with breadcrumb and search */}
      <TopBarLayout 
        breadcrumbItems={["Product", "Product Attribution"]} 
        onSearchChange={handleSearch} 
      />

      {/* Tabs for different attribution states */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', px: 2 }}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          <Tab
            label={
              <Badge badgeContent={statusCounts.all} color="primary">
                All Attributions
              </Badge>
            }
          />
          <Tab
            label={
              <Badge badgeContent={statusCounts.mapped} color="success">
                Mapped
              </Badge>
            }
          />
          <Tab
            label={
              <Badge badgeContent={statusCounts.unmapped} color="error">
                Unmapped
              </Badge>
            }
          />
          <Tab
            label={
              <Badge badgeContent={statusCounts.pending} color="warning">
                Pending
              </Badge>
            }
          />
        </Tabs>
      </Box>

      {/* FilterRow */}
      <FilterRow
        filters={filterConfigs}
        onReset={handleResetFilters}
        onApply={handleApplyFilters}
      />

      {/* Tab Panels with Table */}
      <TabPanel value={currentTab} index={0}>
        {isFilterApplied && (
          <TableComponent
            columns={columns}
            rows={attributions}
            rowsPerPage={pagination.rowsPerPage}
            page={pagination.page}
            totalCount={totalCount}
            onPageChange={handlePageChange}
            showActions={false}
            isLoading={isLoading}
          />
        )}
      </TabPanel>

      <TabPanel value={currentTab} index={1}>
        {isFilterApplied && (
          <TableComponent
            columns={columns}
            rows={attributions}
            rowsPerPage={pagination.rowsPerPage}
            page={pagination.page}
            totalCount={totalCount}
            onPageChange={handlePageChange}
            showActions={false}
            isLoading={isLoading}
          />
        )}
      </TabPanel>

      <TabPanel value={currentTab} index={2}>
        {isFilterApplied && (
          <TableComponent
            columns={columns}
            rows={attributions}
            rowsPerPage={pagination.rowsPerPage}
            page={pagination.page}
            totalCount={totalCount}
            onPageChange={handlePageChange}
            showActions={false}
            isLoading={isLoading}
          />
        )}
      </TabPanel>

      <TabPanel value={currentTab} index={3}>
        {isFilterApplied && (
          <TableComponent
            columns={columns}
            rows={attributions}
            rowsPerPage={pagination.rowsPerPage}
            page={pagination.page}
            totalCount={totalCount}
            onPageChange={handlePageChange}
            showActions={false}
            isLoading={isLoading}
          />
        )}
      </TabPanel>
    </div>
  );
};

export default ProductAttributionPage;
