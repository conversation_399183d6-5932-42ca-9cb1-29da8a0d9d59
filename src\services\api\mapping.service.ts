import axiosInstance from '../../axiosConfig';

// Mapping interfaces for API responses
export interface UnknownMapping {
  PRDNO: string;
  DataSource: string;
  MappingStatus: string;
  DataSourceSortOrder: number;
  DistItemID: string;
  DistItemUPC: string;
  DistItemName: string;
  L4POSSales: number;
  L52POSSales: number;
  TotalPOSSales: number;
  CreatedDate: string | null;
  VCID: number;
}

export interface MappedItem {
  PRDNO: string;
  FromPRDNO: string | null;
  ToPRDNO: string | null;
  DataSource: string;
  DistItemID: string;
  DistItemUPC: string;
  DistItemName: string;
  DESCP: string;
  VCID: number;
  MappingStatus: string;
  LastTransactionDate: string;
  L4POSSales: number;
}

export interface UnknownSearchResponse {
  data: UnknownMapping[];
  total_count: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface MappedSearchResponse {
  mappings: MappedItem[];
  total_count: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface UnknownSearchParams {
  data_source?: string;
  dist_item_id?: string;
  dist_item_upc?: string;
  dist_item_name?: string;
  search_query?: string;
  page?: number;
  limit?: number;
}

export interface MappedSearchParams {
  data_source?: string;
  dist_item_id?: string;
  dist_item_upc?: string;
  dist_item_name?: string;
  prdno?: string;
  descp?: string;
  search_query?: string;
  page?: number;
  limit?: number;
}

// Mapping API service class
class MappingService {
  private baseUrl = 'products/mapping';

  /**
   * Search unknown/unmapped items
   */
  async searchUnknowns(params: UnknownSearchParams): Promise<UnknownSearchResponse> {
    try {
      // Clean up empty parameters
      const cleanParams = Object.entries(params).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);

      const response = await axiosInstance.get(`${this.baseUrl}/unknowns`, {
        params: cleanParams,
      });

      return response.data;
    } catch (error) {
      console.error('Error searching unknown mappings:', error);
      throw new Error('Failed to search unknown mappings');
    }
  }

  /**
   * Search mapped items
   */
  async searchMapped(params: MappedSearchParams): Promise<MappedSearchResponse> {
    try {
      // Clean up empty parameters
      const cleanParams = Object.entries(params).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);

      const response = await axiosInstance.get(`${this.baseUrl}/search`, {
        params: cleanParams,
      });
      
      return response.data;
    } catch (error) {
      console.error('Error searching mapped items:', error);
      throw new Error('Failed to search mapped items');
    }
  }
}

// Export singleton instance
export const mappingService = new MappingService();
export default mappingService;
