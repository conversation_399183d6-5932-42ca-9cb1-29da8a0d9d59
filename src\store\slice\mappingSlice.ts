import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UnknownMapping, MappedItem } from '../../services/api/mapping.service';
import { searchUnknownMappings, searchMappedItems } from '../../services/action/mapping.action';

interface MappingState {
  // Unknown mappings
  unknownMappings: UnknownMapping[];
  unknownMappingsLoading: boolean;
  unknownMappingsError: string | null;
  unknownMappingsTotalCount: number;

  // Mapped items
  mappedItems: MappedItem[];
  mappedItemsLoading: boolean;
  mappedItemsError: string | null;
  mappedItemsTotalCount: number;
  mappedItemsCurrentPage: number;
  mappedItemsHasNext: boolean;
  mappedItemsHasPrevious: boolean;

  // Search and filter states
  unknownSearchQuery: string;
  mappedSearchQuery: string;
  unknownFilters: {
    data_source: string;
    dist_item_id: string;
    dist_item_upc: string;
    dist_item_name: string;
  };
  mappedFilters: {
    data_source: string;
    dist_item_id: string;
    dist_item_upc: string;
    dist_item_name: string;
    prdno: string;
    descp: string;
  };
}

const initialState: MappingState = {
  // Unknown mappings
  unknownMappings: [],
  unknownMappingsLoading: false,
  unknownMappingsError: null,
  unknownMappingsTotalCount: 0,

  // Mapped items
  mappedItems: [],
  mappedItemsLoading: false,
  mappedItemsError: null,
  mappedItemsTotalCount: 0,
  mappedItemsCurrentPage: 1,
  mappedItemsHasNext: false,
  mappedItemsHasPrevious: false,

  // Search and filter states
  unknownSearchQuery: '',
  mappedSearchQuery: '',
  unknownFilters: {
    data_source: '',
    dist_item_id: '',
    dist_item_upc: '',
    dist_item_name: '',
  },
  mappedFilters: {
    data_source: '',
    dist_item_id: '',
    dist_item_upc: '',
    dist_item_name: '',
    prdno: '',
    descp: '',
  },
};

const mappingSlice = createSlice({
  name: 'mapping',
  initialState,
  reducers: {
    // Unknown mappings actions
    setUnknownSearchQuery: (state, action: PayloadAction<string>) => {
      state.unknownSearchQuery = action.payload;
    },
    setUnknownFilters: (state, action: PayloadAction<Partial<typeof initialState.unknownFilters>>) => {
      state.unknownFilters = { ...state.unknownFilters, ...action.payload };
    },
    resetUnknownFilters: (state) => {
      state.unknownFilters = initialState.unknownFilters;
      state.unknownSearchQuery = '';
    },

    // Mapped items actions
    setMappedSearchQuery: (state, action: PayloadAction<string>) => {
      state.mappedSearchQuery = action.payload;
    },
    setMappedFilters: (state, action: PayloadAction<Partial<typeof initialState.mappedFilters>>) => {
      state.mappedFilters = { ...state.mappedFilters, ...action.payload };
    },
    resetMappedFilters: (state) => {
      state.mappedFilters = initialState.mappedFilters;
      state.mappedSearchQuery = '';
    },
  },
  extraReducers: (builder) => {
    // Unknown mappings
    builder
      .addCase(searchUnknownMappings.pending, (state) => {
        state.unknownMappingsLoading = true;
        state.unknownMappingsError = null;
      })
      .addCase(searchUnknownMappings.fulfilled, (state, action) => {
        state.unknownMappingsLoading = false;
        state.unknownMappings = action.payload.data;
        state.unknownMappingsTotalCount = action.payload.total_count;
      })
      .addCase(searchUnknownMappings.rejected, (state, action) => {
        state.unknownMappingsLoading = false;
        state.unknownMappingsError = action.payload || 'Failed to search unknown mappings';
      })

    // Mapped items
      .addCase(searchMappedItems.pending, (state) => {
        state.mappedItemsLoading = true;
        state.mappedItemsError = null;
      })
      .addCase(searchMappedItems.fulfilled, (state, action) => {
        state.mappedItemsLoading = false;
        state.mappedItems = action.payload.mappings;
        state.mappedItemsTotalCount = action.payload.total_count;
        state.mappedItemsCurrentPage = action.payload.page;
        state.mappedItemsHasNext = action.payload.has_next;
        state.mappedItemsHasPrevious = action.payload.has_previous;
      })
      .addCase(searchMappedItems.rejected, (state, action) => {
        state.mappedItemsLoading = false;
        state.mappedItemsError = action.payload || 'Failed to search mapped items';
      });
  },
});

export const {
  setUnknownSearchQuery,
  setUnknownFilters,
  resetUnknownFilters,
  setMappedSearchQuery,
  setMappedFilters,
  resetMappedFilters,
} = mappingSlice.actions;

export default mappingSlice.reducer;
