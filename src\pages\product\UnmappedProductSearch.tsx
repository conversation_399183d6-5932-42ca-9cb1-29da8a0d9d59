// components/UnmappedProductSearch.tsx
import React, { useState, useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import TopBarLayout from "../../components/TopBarLayout";
import TableComponent from "../../components/TableComponent";
import { Box } from "@mui/material";
import FilterRow from "../../components/FilterRow";
import { debounce } from "@mui/material/utils";
import { RootState } from "../../store";
import { ThunkDispatch } from "redux-thunk";
import { UnknownAction } from "@reduxjs/toolkit";
import { searchUnknownMappings } from "../../services/action/mapping.action";
import { setUnknownSearchQuery, setUnknownFilters, resetUnknownFilters } from "../../store/slice/mappingSlice";

// Type alias for dispatch
type AppDispatch = ThunkDispatch<RootState, any, UnknownAction>;

const UnmappedProductSearch = () => {
  const dispatch: AppDispatch = useDispatch();
  const navigate = useNavigate();

  // Get state from Redux store
  const {
    unknownMappings,
    unknownMappingsLoading,
    unknownMappingsError,
    unknownMappingsTotalCount,
    unknownSearchQuery,
    unknownFilters
  } = useSelector((state: RootState) => state.mapping);

  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });

  const [isFilterApplied, setIsFilterApplied] = useState(false);

  // Reset filters when component mounts
  useEffect(() => {
    dispatch(resetUnknownFilters());
  }, [dispatch]);

  // Fetch data function
  const fetchUnknownMappings = useCallback(() => {
    const params = {
      page: pagination.page,
      limit: pagination.rowsPerPage,
      search_query: unknownSearchQuery || undefined,
      data_source: unknownFilters.data_source || undefined,
      dist_item_id: unknownFilters.dist_item_id || undefined,
      dist_item_upc: unknownFilters.dist_item_upc || undefined,
      dist_item_name: unknownFilters.dist_item_name || undefined,
    };

    dispatch(searchUnknownMappings(params));
  }, [dispatch, pagination.page, pagination.rowsPerPage, unknownSearchQuery, unknownFilters]);

  // Debounced search handler (4 seconds delay as per user preference)
  const handleSearch = useCallback(
    debounce((query: string) => {
      dispatch(setUnknownSearchQuery(query));
      setPagination((prev) => ({ ...prev, page: 1 }));
      setIsFilterApplied(true);
    }, 4000),
    [dispatch],
  );

  // Filter change handler
  const handleFilterChange = (name: string, value: string) => {
    dispatch(setUnknownFilters({ [name]: value }));
  };

  // Apply filters handler
  const handleApplyFilters = () => {
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(true);
    fetchUnknownMappings();
  };

  // Reset filters handler
  const handleResetFilters = () => {
    dispatch(resetUnknownFilters());
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(false);
  };

  // Pagination handler
  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  // Handle hyperlink click for DIS. ITEM DESC column
  const handleDistItemDescClick = (vcid: number) => {
    navigate(`/mapping/details?id=${vcid}`);
  };

  // Fetch data when search query changes (debounced search)
  useEffect(() => {
    if (unknownSearchQuery) {
      fetchUnknownMappings();
    }
  }, [unknownSearchQuery, pagination.page, pagination.rowsPerPage, fetchUnknownMappings]);

  // Fetch data when pagination changes and filters are applied
  useEffect(() => {
    if (isFilterApplied && !unknownSearchQuery) {
      fetchUnknownMappings();
    }
  }, [pagination.page, pagination.rowsPerPage, isFilterApplied, fetchUnknownMappings, unknownSearchQuery]);

  // Filter configurations for unmapped product search
  const filterConfigs = [
    {
      name: "data_source",
      label: "Data Source",
      type: "text" as const,
      value: unknownFilters.data_source,
      onChange: (value: string) => handleFilterChange("data_source", value),
    },
    {
      name: "dist_item_id",
      label: "Dist Item ID",
      type: "text" as const,
      value: unknownFilters.dist_item_id,
      onChange: (value: string) => handleFilterChange("dist_item_id", value),
    },
    {
      name: "dist_item_upc",
      label: "Dist Item UPC",
      type: "text" as const,
      value: unknownFilters.dist_item_upc,
      onChange: (value: string) => handleFilterChange("dist_item_upc", value),
    },
    {
      name: "dist_item_name",
      label: "Dist Item Name",
      type: "text" as const,
      value: unknownFilters.dist_item_name,
      onChange: (value: string) => handleFilterChange("dist_item_name", value),
    }
  ];

  // Table columns matching the API response structure
  const columns = [
    {
      id: "DataSource",
      label: "DATA SOURCE",
      description: "Data source identifier",
    },
    {
      id: "DistItemID",
      label: "ITEM ID",
      description: "Item identifier",
    },
    {
      id: "DistItemUPC",
      label: "ITEM UPC",
      description: "Item UPC code",
    },
    {
      id: "DistItemName",
      label: "DIS. ITEM DESC",
      description: "Discontinued item description",
      format: (value: string, row: any) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            textDecoration: "underline",
            cursor: "pointer",
            "&:hover": {
              color: "primary.dark",
              fontWeight: "500",
            },
          }}
          onClick={() => handleDistItemDescClick(row.VCID)}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "CreatedDate",
      label: "LAST DT",
      description: "Last date",
      format: (value: string | null) => value ? new Date(value).toLocaleDateString() : "-",
    },
    {
      id: "L4POSSales",
      label: "LAST 4 POS",
      description: "Last 4 POS sales",
      format: (value: number) => value.toString(),
    },
    {
      id: "L52POSSales",
      label: "LAST 52 POS",
      description: "Last 52 POS sales",
      format: (value: number) => value.toString(),
    },
    {
      id: "TotalPOSSales",
      label: "TOTAL POS",
      description: "Total POS sales",
      format: (value: number) => value.toString(),
    },
    {
      id: "POSPr",
      label: "POS Pr",
      description: "POS Price",
      format: () => "-", // Not available in API response
    },
    {
      id: "PRMSPr",
      label: "PRMS Pr",
      description: "PRMS Price",
      format: () => "-", // Not available in API response
    },
    {
      id: "InvOH",
      label: "Inv OH",
      description: "Inventory On Hand",
      format: () => "-", // Not available in API response
    },
    {
      id: "MappingStatus",
      label: "Status",
      description: "Mapping status",
    },
  ];

  return (
    <div>
      {/* TopBarLayout with breadcrumb and search */}
      <TopBarLayout
        breadcrumbItems={["Product", "Unmapped Search"]}
        onSearchChange={handleSearch}
      />

      {/* FilterRow with unmapped product search fields */}
      <FilterRow
        filters={filterConfigs}
        onReset={handleResetFilters}
        onApply={handleApplyFilters}
      />

      {/* Table component - only show when filters are applied */}
      {isFilterApplied && (
        <TableComponent
          columns={columns}
          rows={unknownMappings}
          rowsPerPage={pagination.rowsPerPage}
          page={pagination.page}
          totalCount={unknownMappingsTotalCount}
          onPageChange={handlePageChange}
          showActions={false}
          isLoading={unknownMappingsLoading}
        />
      )}

      {/* Show error message if there's an error */}
      {unknownMappingsError && (
        <Box sx={{ p: 3, textAlign: "center", color: "error.main" }}>
          Error: {unknownMappingsError}
        </Box>
      )}
    </div>
  );
};

export default UnmappedProductSearch;
