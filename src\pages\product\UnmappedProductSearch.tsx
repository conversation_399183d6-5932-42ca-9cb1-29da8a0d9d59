// components/UnmappedProductSearch.tsx
import React, { useState, useCallback, useEffect } from "react";
import TopBarLayout from "../../components/TopBarLayout";
import TableComponent from "../../components/TableComponent";
import { Box } from "@mui/material";
import FilterRow from "../../components/FilterRow";
import { debounce } from "@mui/material/utils";

// Unmapped Product interface matching the table structure from image
interface UnmappedProduct {
  DataSource: string;
  ItemID: string;
  ItemUPC: string;
  DiscItemDesc: string;
  LastDY: string;
  LastPos: string;
  TotalPos: string;
  TotalPOS: string;
  InvOff: string;
  Status: string;
}

// Mock data for unmapped products based on the image
const mockUnmappedProducts: UnmappedProduct[] = [
  {
    DataSource: "Pan Pacific Pet",
    ItemID: "0",
    ItemUPC: "Unknown",
    DiscItemDesc: "Greenies CAT PILL Pockets Salmon 1.6oz",
    LastDY: "12/23/2015",
    LastPos: "$0",
    TotalPos: "$21,183",
    TotalPOS: "56625",
    InvOff: "",
    Status: "Active"
  },
  {
    DataSource: "American Distributing",
    ItemID: "0",
    ItemUPC: "006642863072142",
    DiscItemDesc: "Greenies PP Cat Salmon",
    LastDY: "12/23/2015",
    LastPos: "$1,118",
    TotalPos: "$108,848",
    TotalPOS: "505",
    InvOff: "",
    Status: "Active"
  },
  {
    DataSource: "American Distributing",
    ItemID: "0",
    ItemUPC: "006642863072142",
    DiscItemDesc: "Greenies PP Cat Salmon",
    LastDY: "12/23/2015",
    LastPos: "$0",
    TotalPos: "$45,948",
    TotalPOS: "1183860",
    InvOff: "",
    Status: "Active"
  },
  {
    DataSource: "Fauna Food Corp",
    ItemID: "0",
    ItemUPC: "006642863072142",
    DiscItemDesc: "Greenies PP Cat Salmon",
    LastDY: "12/23/2015",
    LastPos: "$0",
    TotalPos: "$766",
    TotalPOS: "145716",
    InvOff: "",
    Status: "Active"
  },
  {
    DataSource: "United Pacific pet",
    ItemID: "0",
    ItemUPC: "006642863072142",
    DiscItemDesc: "Greenies PP Cat Salmon",
    LastDY: "12/23/2015",
    LastPos: "$0",
    TotalPos: "$2,575",
    TotalPOS: "94247",
    InvOff: "",
    Status: "Active"
  },
  {
    DataSource: "United Pacific pet",
    ItemID: "0",
    ItemUPC: "006642863072142",
    DiscItemDesc: "Greenies CAT PILL Pockets Salmon 1.6oz",
    LastDY: "",
    LastPos: "",
    TotalPos: "",
    TotalPOS: "100908",
    InvOff: "",
    Status: "Active"
  },
  {
    DataSource: "United Pacific pet",
    ItemID: "0",
    ItemUPC: "006642863072142",
    DiscItemDesc: "Greenies PP Cat Salmon",
    LastDY: "",
    LastPos: "",
    TotalPos: "",
    TotalPOS: "70710",
    InvOff: "",
    Status: "Active"
  },
  {
    DataSource: "Illo Dental",
    ItemID: "0",
    ItemUPC: "Unknown",
    DiscItemDesc: "Greenies PP Cat Salmon",
    LastDY: "12/23/2015",
    LastPos: "$0",
    TotalPos: "$66,919",
    TotalPOS: "61911",
    InvOff: "",
    Status: "Active"
  },
  {
    DataSource: "Pan Pacific Pet",
    ItemID: "0",
    ItemUPC: "Unknown",
    DiscItemDesc: "Greenies PP Cat Salmon",
    LastDY: "12/23/2015",
    LastPos: "$0",
    TotalPos: "$24,000",
    TotalPOS: "56204",
    InvOff: "",
    Status: "Active"
  }
];

const UnmappedProductSearch = () => {
  // Local state for unmapped product search
  const [products, setProducts] = useState<UnmappedProduct[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [genericSearch, setGenericSearch] = useState("");
  const [isFilterApplied, setIsFilterApplied] = useState(false);
  const [filterTrigger, setFilterTrigger] = useState(0);
  
  // Filter state for unmapped product search fields
  const [filters, setFilters] = useState({
    DataSource: "",
    ItemID: "",
    ItemUPC: "",
    DiscItemDesc: "",
    Status: ""
  });

  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });

  // Filter configurations for unmapped product search
  const filterConfigs = [
    {
      name: "DataSource",
      label: "Data Source",
      type: "text" as const,
      value: filters.DataSource,
      onChange: (value: string) => handleFilterChange("DataSource", value),
    },
    {
      name: "ItemID",
      label: "Item ID",
      type: "text" as const,
      value: filters.ItemID,
      onChange: (value: string) => handleFilterChange("ItemID", value),
    },
    {
      name: "ItemUPC",
      label: "Item UPC",
      type: "text" as const,
      value: filters.ItemUPC,
      onChange: (value: string) => handleFilterChange("ItemUPC", value),
    },
    {
      name: "DiscItemDesc",
      label: "Disc. Item Desc",
      type: "text" as const,
      value: filters.DiscItemDesc,
      onChange: (value: string) => handleFilterChange("DiscItemDesc", value),
    },
    {
      name: "Status",
      label: "Status",
      type: "text" as const,
      value: filters.Status,
      onChange: (value: string) => handleFilterChange("Status", value),
    }
  ];

  // Mock API call simulation for unmapped products
  const fetchProducts = useCallback(() => {
    setIsLoading(true);
    
    // Simulate API delay
    setTimeout(() => {
      let filteredProducts = [...mockUnmappedProducts];
      
      // Apply generic search
      if (genericSearch) {
        filteredProducts = filteredProducts.filter(product =>
          Object.values(product).some(value =>
            value?.toString().toLowerCase().includes(genericSearch.toLowerCase())
          )
        );
      }
      
      // Apply specific filters only when isFilterApplied is true
      if (isFilterApplied) {
        if (filters.DataSource) {
          filteredProducts = filteredProducts.filter(product =>
            product.DataSource.toLowerCase().includes(filters.DataSource.toLowerCase())
          );
        }
        
        if (filters.ItemID) {
          filteredProducts = filteredProducts.filter(product =>
            product.ItemID.toLowerCase().includes(filters.ItemID.toLowerCase())
          );
        }
        
        if (filters.ItemUPC) {
          filteredProducts = filteredProducts.filter(product =>
            product.ItemUPC.toLowerCase().includes(filters.ItemUPC.toLowerCase())
          );
        }
        
        if (filters.DiscItemDesc) {
          filteredProducts = filteredProducts.filter(product =>
            product.DiscItemDesc.toLowerCase().includes(filters.DiscItemDesc.toLowerCase())
          );
        }
        
        if (filters.Status) {
          filteredProducts = filteredProducts.filter(product =>
            product.Status.toLowerCase().includes(filters.Status.toLowerCase())
          );
        }
      }
      
      // Apply pagination
      const startIndex = (pagination.page - 1) * pagination.rowsPerPage;
      const endIndex = startIndex + pagination.rowsPerPage;
      const paginatedProducts = filteredProducts.slice(startIndex, endIndex);
      
      setProducts(paginatedProducts);
      setTotalCount(filteredProducts.length);
      setIsLoading(false);
    }, 500);
  }, [genericSearch, pagination.page, pagination.rowsPerPage, isFilterApplied, filters]);

  // Debounced search handler - only for search bar
  const handleSearch = useCallback(
    debounce((query: string) => {
      setGenericSearch(query);
      setPagination((prev) => ({ ...prev, page: 1 }));
      setIsFilterApplied(true);
    }, 500),
    [],
  );

  // Filter change handler
  const handleFilterChange = (name: string, value: string) => {
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // Apply filters handler
  const handleApplyFilters = () => {
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(true);
    // Increment trigger to force re-fetch even when isFilterApplied is already true
    setFilterTrigger(prev => prev + 1);
  };

  // Reset filters handler
  const handleResetFilters = () => {
    setFilters({
      DataSource: "",
      ItemID: "",
      ItemUPC: "",
      DiscItemDesc: "",
      Status: ""
    });
    setGenericSearch("");
    setPagination((prev) => ({ ...prev, page: 1 }));
    setIsFilterApplied(false);
    setFilterTrigger(0);
    setProducts([]);
    setTotalCount(0);
  };

  // Pagination handler
  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  // Fetch products when search, pagination, or filter trigger changes
  useEffect(() => {
    if (genericSearch || isFilterApplied) {
      fetchProducts();
    }
  }, [genericSearch, pagination.page, pagination.rowsPerPage, isFilterApplied, filterTrigger]);

  // Table columns matching the unmapped product structure
  const columns = [
    {
      id: "DataSource",
      label: "DATA SOURCE",
      description: "Data source identifier",
    },
    {
      id: "ItemID",
      label: "ITEM ID",
      description: "Item identifier",
    },
    {
      id: "ItemUPC",
      label: "ITEM UPC",
      description: "Item UPC code",
    },
    {
      id: "DiscItemDesc",
      label: "DIS. ITEM DESC",
      description: "Discontinued item description",
      format: (value: string) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            textDecoration: "underline",
            cursor: "pointer",
            "&:hover": {
              color: "primary.dark",
              fontWeight: "500",
            },
          }}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "LastDY",
      label: "LAST DY",
      description: "Last day",
    },
    {
      id: "Last4Pos",
      label: "LAST 4 POS",
      description: "Last position",
    },
    {
      id: "Last52Pos",
      label: "Last 52 POS",
      description: "Total position",
    },
    {
      id: "TotalPOS",
      label: "TOTAL POS",
      description: "Total POS value",
    },
    {
      id: "InvOff",
      label: "INV OFF",
      description: "Inventory off",
    },
    {
      id: "Status",
      label: "STATUS",
      description: "Product status",
      format: (value: string) => (
        <Box
          component="span"
          sx={{
            color: value === "Active" ? "success.main" : "error.main",
            fontWeight: "500",
            display: "flex",
            alignItems: "center",
            gap: 0.5,
          }}
        >
          <Box
            component="span"
            sx={{
              width: 8,
              height: 8,
              borderRadius: "50%",
              backgroundColor: value === "Active" ? "success.main" : "error.main",
            }}
          />
          {value}
        </Box>
      ),
    },
  ];

  return (
    <div>
      {/* TopBarLayout with breadcrumb and search */}
      <TopBarLayout 
        breadcrumbItems={["Product", "Unmapped Search"]} 
        onSearchChange={handleSearch} 
      />

      {/* FilterRow with unmapped product search fields */}
      <FilterRow
        filters={filterConfigs}
        onReset={handleResetFilters}
        onApply={handleApplyFilters}
      />

      {/* Table component - only show when filters are applied */}
      {isFilterApplied && (
        <TableComponent
          columns={columns}
          rows={products}
          rowsPerPage={pagination.rowsPerPage}
          page={pagination.page}
          totalCount={totalCount}
          onPageChange={handlePageChange}
          showActions={false}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default UnmappedProductSearch;
