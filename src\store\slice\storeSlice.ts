// store/slice/storeSlice.ts
import { createSlice } from "@reduxjs/toolkit";
import {
  createStore,
  deleteStores,
  findStores,
  getAllStores,
  getChangeLog,
  getMappingDetails,
  getNutroStoreDetail,
  getNutroStoreMetadata,
  getSearchStores,
  getStoreFilterValues,
  getStoreSuggestions,
  searchDistStores,
  searchUnmappedStores,
  updateStore,
  updateStoreMapping,
  updateStoreMappingMetadata,
} from "../../services/action/store.action";
import { StoreState } from "../../types/store.types";

const initialState: StoreState = {
  generic_search: "",
  filters: {
    StoreName: "",
    DistStoreID: "",
    DataSource: "",
    City: "",
    State: "",
    ZipCode: "",
    NutroStoreID: "",
    Address: "",
    DistStorePhone: "",
    DistStoreZip: "",
    DistStoreState: "",
    DistStoreCity: "",
    DistStoreAddress: "",
    DistStoreName: "",
  },
  stores: [],
  distStores: [],
  storeDetails: null,
  storeMappings: [],
  nutroStoreDetailtotalCount: 0,
  storeMetadata: null,
  isLoading: false,
  error: null,
  totalCount: 0,
  distStoresTotalCount: 0,
  unmappedStores: [],
  unmappedStoresTotalCount: 0,
  changeLog: [],
  changeLogTotalCount: 0,
  isChangeLogModalOpen: false,
  storeSuggestions: [],
  storeSuggestionsTotalCount: 0,
  isCreatingStore: false,
  createStoreError: null,
  createStoreSuccess: false,
  createdStoreId: null,
  mappingDetails: [],
  mappingDetailsTotalCount: 0,
  isUpdatingMapping: false,
  updateMappingError: null,
  updateMappingSuccess: false,
  isUpdatingMetadata: false,
  updateMetadataError: null,
  updateMetadataSuccess: false,
  updatedMetadataFields: [],
  storeAttributions: [],
  storeAttributionTotal: 0,
  storeFilterValues: {
    store_ids: { values: [], total: 0 },
    store_names: { values: [], total: 0 },
    chain_names: { values: [], total: 0 },
    territory_managers: { values: [], total: 0 },
    district_managers: { values: [], total: 0 },
    store_numbers: { values: [], total: 0 },
    account_types: { values: [], total: 0 },
    report_categories: { values: [], total: 0 },
  },
  isUpdatingStore: false,
  updateStoreError: null,
  isDeletingStores: false,
  deleteStoresError: null,
  deleteStoresResult: null,
  searchResults: [],
  searchTotalCount: 0,
  isSearching: false,
  searchError: null,
};

const storeSlice = createSlice({
  name: "store",
  initialState,
  reducers: {
    setSearchQuery(state, action) {
      state.generic_search = action.payload;
    },
    setStoreFilters(state, action) {
      state.filters = {
        ...state.filters,
        ...action.payload,
      };
    },
    resetStoreFilters(state) {
      state.filters = initialState.filters;
      state.generic_search = initialState.generic_search;
    },
    openChangeLogModal(state) {
      state.isChangeLogModalOpen = true;
    },
    closeChangeLogModal(state) {
      state.isChangeLogModalOpen = false;
    },
    resetCreateStoreState(state) {
      state.isCreatingStore = false;
      state.createStoreError = null;
      state.createStoreSuccess = false;
      state.createdStoreId = null;
    },
    resetMappingState(state) {
      state.isUpdatingMapping = false;
      state.updateMappingError = null;
      state.updateMappingSuccess = false;
    },
    resetMetadataState(state) {
      state.isUpdatingMetadata = false;
      state.updateMetadataError = null;
      state.updateMetadataSuccess = false;
      state.updatedMetadataFields = [];
    },
    resetStoreAttributionState(state) {
      state.storeAttributions = [];
      state.storeAttributionTotal = 0;
    },
    resetUpdateStoreState(state) {
      state.isUpdatingStore = false;
      state.updateStoreError = null;
    },
    resetDeleteStoresState(state) {
      state.isDeletingStores = false;
      state.deleteStoresError = null;
      state.deleteStoresResult = null;
    },
    resetSearchResults(state) {
      state.searchResults = [];
      state.searchTotalCount = 0;
      state.searchError = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSearchStores.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSearchStores.fulfilled, (state, action) => {
        state.isLoading = false;
        state.stores = action.payload.stores;
        state.totalCount = action.payload.totalCount;
      })
      .addCase(getSearchStores.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to fetch stores";
      })
      .addCase(getNutroStoreDetail.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getNutroStoreDetail.fulfilled, (state, action) => {
        state.isLoading = false;
        state.storeMappings = action.payload.stores;
        state.nutroStoreDetailtotalCount = action.payload.totalCount;
      })
      .addCase(getNutroStoreDetail.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to fetch store details";
      })
      .addCase(getNutroStoreMetadata.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getNutroStoreMetadata.fulfilled, (state, action) => {
        state.isLoading = false;
        state.storeMetadata = action.payload.stores[0]; // Assuming we want the first record
      })
      .addCase(getNutroStoreMetadata.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to fetch store metadata";
      })
      .addCase(searchDistStores.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchDistStores.fulfilled, (state, action) => {
        state.isLoading = false;
        state.distStores = action.payload.stores;
        state.distStoresTotalCount = action.payload.totalCount;
      })
      .addCase(searchDistStores.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to fetch dist stores";
      })
      .addCase(searchUnmappedStores.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(searchUnmappedStores.fulfilled, (state, action) => {
        state.isLoading = false;
        state.unmappedStores = action.payload.stores;
        state.unmappedStoresTotalCount = action.payload.totalCount;
      })
      .addCase(searchUnmappedStores.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to fetch unmapped stores";
      })
      .addCase(getChangeLog.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getChangeLog.fulfilled, (state, action) => {
        state.isLoading = false;
        state.changeLog = action.payload.changes;
        state.changeLogTotalCount = action.payload.totalCount;
      })
      .addCase(getChangeLog.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to fetch change log";
      })
      .addCase(getStoreSuggestions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getStoreSuggestions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.storeSuggestions = action.payload.suggestions;
        state.storeSuggestionsTotalCount = action.payload.totalCount;
      })
      .addCase(getStoreSuggestions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to fetch store suggestions";
      })
      .addCase(createStore.pending, (state) => {
        state.isCreatingStore = true;
        state.createStoreError = null;
        state.createStoreSuccess = false;
        state.createdStoreId = null;
      })
      .addCase(createStore.fulfilled, (state, action) => {
        state.isCreatingStore = false;
        state.createStoreSuccess = action.payload.success;
        state.createdStoreId = action.payload.nutroStoreId || null;
      })
      .addCase(createStore.rejected, (state, action) => {
        state.isCreatingStore = false;
        state.createStoreError = (action.payload as string) || "Failed to create store";
      })
      .addCase(getMappingDetails.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getMappingDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.mappingDetails = action.payload.mappings;
        state.mappingDetailsTotalCount = action.payload.totalCount;
      })
      .addCase(getMappingDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to fetch mapping details";
      })
      .addCase(updateStoreMapping.pending, (state) => {
        state.isUpdatingMapping = true;
        state.updateMappingError = null;
        state.updateMappingSuccess = false;
      })
      .addCase(updateStoreMapping.fulfilled, (state) => {
        state.isUpdatingMapping = false;
        state.updateMappingSuccess = true;
      })
      .addCase(updateStoreMapping.rejected, (state, action) => {
        state.isUpdatingMapping = false;
        state.updateMappingError = (action.payload as string) || "Failed to update mapping";
      })
      .addCase(updateStoreMappingMetadata.pending, (state) => {
        state.isUpdatingMetadata = true;
        state.updateMetadataError = null;
        state.updateMetadataSuccess = false;
        state.updatedMetadataFields = [];
      })
      .addCase(updateStoreMappingMetadata.fulfilled, (state, action) => {
        state.isUpdatingMetadata = false;
        state.updateMetadataSuccess = true;
        state.updatedMetadataFields = action.payload.updated_fields;
      })
      .addCase(updateStoreMappingMetadata.rejected, (state, action) => {
        state.isUpdatingMetadata = false;
        state.updateMetadataError = (action.payload as string) || "Failed to update metadata";
      })
      .addCase(getAllStores.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllStores.fulfilled, (state, action) => {
        state.isLoading = false;
        state.storeAttributions = action.payload.stores;
        state.storeAttributionTotal = action.payload.total_count;
      })
      .addCase(getAllStores.rejected, (state, action) => {
        state.isLoading = false;
        state.error = (action.payload as string) || "Failed to fetch stores";
      })
      .addCase(getStoreFilterValues.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getStoreFilterValues.fulfilled, (state, action) => {
        state.isLoading = false;
        const { field, values, total } = action.payload;
        state.storeFilterValues[field] = { values, total };
      })
      .addCase(getStoreFilterValues.rejected, (state, action) => {
        state.isLoading = false;
        state.error = (action.payload as string) || "Failed to fetch filter values";
      })
      .addCase(updateStore.pending, (state) => {
        state.isUpdatingStore = true;
        state.updateStoreError = null;
      })
      .addCase(updateStore.fulfilled, (state, action) => {
        state.isUpdatingStore = false;
        // Update the store in the list if it exists
        const index = state.storeAttributions.findIndex(
          (store) => store.NutroStoreID === action.payload.NutroStoreID,
        );
        if (index !== -1) {
          state.storeAttributions[index] = action.payload;
        }
      })
      .addCase(updateStore.rejected, (state, action) => {
        state.isUpdatingStore = false;
        state.updateStoreError = (action.payload as string) || "Failed to update store";
      })
      .addCase(deleteStores.pending, (state) => {
        state.isDeletingStores = true;
        state.deleteStoresError = null;
        state.deleteStoresResult = null;
      })
      .addCase(deleteStores.fulfilled, (state, action) => {
        state.isDeletingStores = false;
        state.deleteStoresResult = action.payload;
        // Remove deleted stores from the list
        state.storeAttributions = state.storeAttributions.filter(
          (store) => !action.payload.not_found_ids.includes(store.NutroStoreID || 0),
        );
      })
      .addCase(deleteStores.rejected, (state, action) => {
        state.isDeletingStores = false;
        state.deleteStoresError = (action.payload as string) || "Failed to delete stores";
      })
      .addCase(findStores.pending, (state) => {
        state.isSearching = true;
        state.searchError = null;
      })
      .addCase(findStores.fulfilled, (state, action) => {
        state.isSearching = false;
        state.searchResults = action.payload.results;
        state.searchTotalCount = action.payload.total_count;
      })
      .addCase(findStores.rejected, (state, action) => {
        state.isSearching = false;
        state.searchError = (action.payload as string) || "Failed to search stores";
      });
  },
});

export const {
  setSearchQuery,
  setStoreFilters,
  resetStoreFilters,
  openChangeLogModal,
  closeChangeLogModal,
  resetMappingState,
  resetMetadataState,
  resetSearchResults,
} = storeSlice.actions;

export default storeSlice.reducer;
