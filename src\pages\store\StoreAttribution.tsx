import React, { useState, useEffect, useCallback } from "react";
import TopBarLayout from "../../components/TopBarLayout";
import TableComponent from "../../components/TableComponent";
import { Box, debounce, Button, LinearProgress } from "@mui/material";
import FilterRow from "../../components/FilterRow";
import { useNavigate } from "react-router-dom";
import { setSearchQuery, resetStoreFilters } from "../../store/slice/storeSlice";
import {
  getAllStores,
  getStoreFilterValues,
  deleteStores,
  getAllStoresTotal,
  updateStore,
} from "../../services/action/store.action";
import { ThunkDispatch } from "redux-thunk";
import { UnknownAction } from "@reduxjs/toolkit";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import InfiniteScrollDropdown from "../../components/InfiniteScroll";
import * as XLSX from "xlsx";
import { toast } from "react-toastify";
import EditStoreModal from "../../components/EditStoreAttribution";
import ConfirmationModal from "../../components/store/ConfirmationModal";

type AppDispatch = ThunkDispatch<RootState, unknown, UnknownAction>;

interface StoreAttributionItem {
  NutroStoreID: number;
  StoreName: string;
  LocationAddress: string;
  LocationCity: string;
  LocationState: string;
  LocationZipCode: string;
  LocationCountry: string;
  Phone: string;
  ChainName: string;
  StoreNumber: string;
  AccountType: string;
  TerritoryManager: string;
  DistrictManager: string;
  StoreStatus: string;
  ReportCategory: string;
}

const StoreAttributionPage = () => {
  const dispatch: AppDispatch = useDispatch();
  const navigate = useNavigate();
  const { storeAttributions, isLoading, storeFilterValues, storeAttributionTotal } = useSelector(
    (state: RootState) => state.store,
  );

  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  const [selectedStores, setSelectedStores] = useState<number[]>([]);
  const [workingFilters, setWorkingFilters] = useState({
    store_ids: [] as string[],
    store_names: [] as string[],
    chain_names: [] as string[],
    territory_managers: [] as string[],
    district_managers: [] as string[],
    store_numbers: [] as string[],
    account_types: [] as string[],
    report_categories: [] as string[],
  });

  const [appliedFilters, setAppliedFilters] = useState({
    store_ids: [] as string[],
    store_names: [] as string[],
    chain_names: [] as string[],
    territory_managers: [] as string[],
    district_managers: [] as string[],
    store_numbers: [] as string[],
    account_types: [] as string[],
    report_categories: [] as string[],
  });
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [selectedStore, setSelectedStore] = useState<StoreAttributionItem | null>(null);

  // Add this handler function
  const handleEditStore = (store: StoreAttributionItem) => {
    setSelectedStore(store);
    setEditModalOpen(true);
  };
  const fetchStores = useCallback(() => {
    if (searchQuery && searchQuery.length < 3) return;
    dispatch(
      getAllStores({
        page: pagination.page,
        limit: pagination.rowsPerPage,
        search_query: searchQuery,
        store_ids:
          appliedFilters.store_ids.length > 0 ? appliedFilters.store_ids.map(Number) : undefined,
        store_names: appliedFilters.store_names.length > 0 ? appliedFilters.store_names : undefined,
        chain_names: appliedFilters.chain_names.length > 0 ? appliedFilters.chain_names : undefined,
        territory_managers:
          appliedFilters.territory_managers.length > 0
            ? appliedFilters.territory_managers
            : undefined,
        district_managers:
          appliedFilters.district_managers.length > 0
            ? appliedFilters.district_managers
            : undefined,
        store_numbers:
          appliedFilters.store_numbers.length > 0 ? appliedFilters.store_numbers : undefined,
        account_types:
          appliedFilters.account_types.length > 0 ? appliedFilters.account_types : undefined,
        report_categories:
          appliedFilters.report_categories.length > 0
            ? appliedFilters.report_categories
            : undefined,
      }),
    );
  }, [dispatch, pagination.page, pagination.rowsPerPage, searchQuery, appliedFilters]);

  // Initial fetch and when pagination or applied filters change
  useEffect(() => {
    fetchStores();
  }, [fetchStores]);

  // Update your search handler
  const handleSearch = useCallback(
    (query: string) => {
      setSearchQuery(query);
      setPagination((prev) => ({ ...prev, page: 1 }));

      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }

      if (query.length === 0 || query.length >= 3) {
        const timeout = setTimeout(() => {
          dispatch(
            getAllStores({
              page: 1,
              limit: pagination.rowsPerPage,
              search_query: query,
              store_ids:
                appliedFilters.store_ids.length > 0
                  ? appliedFilters.store_ids.map(Number)
                  : undefined,
              store_names:
                appliedFilters.store_names.length > 0 ? appliedFilters.store_names : undefined,
              chain_names:
                appliedFilters.chain_names.length > 0 ? appliedFilters.chain_names : undefined,
              territory_managers:
                appliedFilters.territory_managers.length > 0
                  ? appliedFilters.territory_managers
                  : undefined,
              district_managers:
                appliedFilters.district_managers.length > 0
                  ? appliedFilters.district_managers
                  : undefined,
              store_numbers:
                appliedFilters.store_numbers.length > 0 ? appliedFilters.store_numbers : undefined,
              account_types:
                appliedFilters.account_types.length > 0 ? appliedFilters.account_types : undefined,
              report_categories:
                appliedFilters.report_categories.length > 0
                  ? appliedFilters.report_categories
                  : undefined,
            }),
          );
        }, 500);
        setSearchTimeout(timeout);
      }
    },
    [dispatch, searchTimeout, pagination.rowsPerPage, appliedFilters],
  );

  const handleFilterChange = (name: keyof typeof workingFilters, value: string[]) => {
    setWorkingFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Don't fetch immediately - wait for Apply click
  };

  const handleApplyFilters = () => {
    setAppliedFilters(workingFilters);
    setPagination((prev) => ({ ...prev, page: 1 }));
    // The useEffect with appliedFilters dependency will trigger the fetch
  };

  const handleResetFilters = () => {
    const resetFilters = {
      store_ids: [],
      store_names: [],
      chain_names: [],
      territory_managers: [],
      district_managers: [],
      store_numbers: [],
      account_types: [],
      report_categories: [],
    };
    setWorkingFilters(resetFilters);
    setAppliedFilters(resetFilters);
    setPagination((prev) => ({ ...prev, page: 1 }));
    // The useEffect with appliedFilters dependency will trigger the fetch
  };

  // Rest of the component remains the same...
  const handleStoreClick = (storeId: number) => {
    navigate(`/store-details/${storeId}`);
  };

  // Download progress state
  const [downloadProgress, setDownloadProgress] = useState({
    inProgress: false,
    progress: 0,
  });

  const handleDownloadExcel = async () => {
    if (downloadProgress.inProgress) return;

    const toastId = toast.loading("Preparing download...", {
      autoClose: false,
      closeButton: false,
    });

    try {
      setDownloadProgress({ inProgress: true, progress: 0 });

      // First get the total count to estimate progress
      const countResponse = await dispatch(
        getAllStoresTotal({
          count_only: true,
          search_query: searchQuery,
          store_ids:
            appliedFilters.store_ids.length > 0 ? appliedFilters.store_ids.map(Number) : undefined,
          store_names:
            appliedFilters.store_names.length > 0 ? appliedFilters.store_names : undefined,
          chain_names:
            appliedFilters.chain_names.length > 0 ? appliedFilters.chain_names : undefined,
          territory_managers:
            appliedFilters.territory_managers.length > 0
              ? appliedFilters.territory_managers
              : undefined,
          district_managers:
            appliedFilters.district_managers.length > 0
              ? appliedFilters.district_managers
              : undefined,
          store_numbers:
            appliedFilters.store_numbers.length > 0 ? appliedFilters.store_numbers : undefined,
          account_types:
            appliedFilters.account_types.length > 0 ? appliedFilters.account_types : undefined,
          report_categories:
            appliedFilters.report_categories.length > 0
              ? appliedFilters.report_categories
              : undefined,
        }),
      ).unwrap();

      const totalCount = countResponse.total_count;
      if (totalCount === 0) {
        toast.update(toastId, {
          render: "No data available to export",
          type: "info",
          isLoading: false,
          autoClose: 3000,
        });
        return;
      }

      toast.update(toastId, {
        render: (
          <div>
            <div>Downloading {totalCount.toLocaleString()} records...</div>
            <LinearProgress
              variant="determinate"
              value={downloadProgress.progress}
              sx={{ mt: 1, height: 6 }}
            />
          </div>
        ),
        isLoading: true,
      });

      // Now fetch all data in chunks
      const chunkSize = 50000; // Adjust based on your server capabilities
      const chunks = Math.ceil(totalCount / chunkSize);
      let allStores: any[] = [];

      for (let i = 0; i < chunks; i++) {
        const response = await dispatch(
          getAllStoresTotal({
            limit: chunkSize,
            offset: i * chunkSize,
            search_query: searchQuery,
            store_ids:
              appliedFilters.store_ids.length > 0
                ? appliedFilters.store_ids.map(Number)
                : undefined,
            store_names:
              appliedFilters.store_names.length > 0 ? appliedFilters.store_names : undefined,
            chain_names:
              appliedFilters.chain_names.length > 0 ? appliedFilters.chain_names : undefined,
            territory_managers:
              appliedFilters.territory_managers.length > 0
                ? appliedFilters.territory_managers
                : undefined,
            district_managers:
              appliedFilters.district_managers.length > 0
                ? appliedFilters.district_managers
                : undefined,
            store_numbers:
              appliedFilters.store_numbers.length > 0 ? appliedFilters.store_numbers : undefined,
            account_types:
              appliedFilters.account_types.length > 0 ? appliedFilters.account_types : undefined,
            report_categories:
              appliedFilters.report_categories.length > 0
                ? appliedFilters.report_categories
                : undefined,
          }),
        ).unwrap();

        allStores = [...allStores, ...response.stores];

        // Update progress
        const progress = Math.min(100, Math.round((allStores.length / totalCount) * 100));
        setDownloadProgress((prev) => ({ ...prev, progress }));
      }

      // Create Excel file
      toast.update(toastId, {
        render: "Creating Excel file...",
      });

      const ws = XLSX.utils.json_to_sheet(allStores);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Stores");

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const filename = `Stores_Export_${timestamp}.xlsx`;

      XLSX.writeFile(wb, filename);

      toast.update(toastId, {
        render: `Download complete! Saved as ${filename}`,
        type: "success",
        isLoading: false,
        autoClose: 5000,
      });
    } catch (error: any) {
      console.error("Error downloading Excel:", error);
      toast.update(toastId, {
        render: error.message || "Failed to download Excel file",
        type: "error",
        isLoading: false,
        autoClose: 5000,
      });
    } finally {
      setDownloadProgress({ inProgress: false, progress: 0 });
    }
  };
  // Add a useEffect to clean up the timeout
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);
  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = storeAttributions.map((store) => store.NutroStoreID || 0);
      setSelectedStores(newSelected);
      return;
    }
    setSelectedStores([]);
  };

  const handleSelectClick = (event: React.MouseEvent<unknown>, id: number) => {
    const selectedIndex = selectedStores.indexOf(id);
    let newSelected: number[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selectedStores, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selectedStores.slice(1));
    } else if (selectedIndex === selectedStores.length - 1) {
      newSelected = newSelected.concat(selectedStores.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selectedStores.slice(0, selectedIndex),
        selectedStores.slice(selectedIndex + 1),
      );
    }

    setSelectedStores(newSelected);
  };

  const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false);

  // Replace your handleDeleteSelected function with these:
  const handleDeleteClick = () => {
    if (selectedStores.length === 0) return;
    setDeleteConfirmationOpen(true);
  };

  const handleDeleteConfirmed = () => {
    setDeleteConfirmationOpen(false);

    // Instead of deleting, we'll update the StoreStatus to "D" for each selected store
    const updatePromises = selectedStores.map((storeId) =>
      dispatch(
        updateStore({
          nutro_store_id: storeId,
          updateData: { StoreStatus: "D" },
        }),
      ),
    );

    Promise.all(updatePromises)
      .then(() => {
        // Reset all filters and state
        const resetFilters = {
          store_ids: [],
          store_names: [],
          chain_names: [],
          territory_managers: [],
          district_managers: [],
          store_numbers: [],
          account_types: [],
          report_categories: [],
        };

        setWorkingFilters(resetFilters);
        setAppliedFilters(resetFilters);
        setSearchQuery("");
        setSelectedStores([]);
        setPagination({ page: 1, rowsPerPage: 10 });

        // Force a reload of the data with default parameters
        dispatch(
          getAllStores({
            page: 1,
            limit: 10,
          }),
        );

        toast.success(`${selectedStores.length} stores marked as inactive successfully`);
      })
      .catch((error) => {
        toast.error(`Failed to update stores: ${error.message || "Unknown error"}`);
      });
  };

  // Update your delete button in actionButtons:
  const actionButtons = (
    <Box sx={{ display: "flex", gap: 2, ml: 2 }}>
      <Button
        variant="contained"
        color="primary"
        onClick={handleDownloadExcel}
        disabled={isLoading || downloadProgress.inProgress}
      >
        {downloadProgress.inProgress
          ? `Exporting (${downloadProgress.progress}%)`
          : "Download Excel"}
      </Button>
      <Button
        variant="contained"
        color="error"
        disabled={selectedStores.length === 0}
        onClick={handleDeleteClick}
      >
        Delete Selected ({selectedStores.length})
      </Button>
    </Box>
  );

  const columns = [
    {
      id: "NutroStoreID",
      label: "Store ID",
      description: "Nutro Store ID",
      format: (value: number, row: StoreAttributionItem) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            textDecoration: "underline",
            cursor: "pointer",
            "&:hover": {
              color: "primary.dark",
              fontWeight: "500",
            },
          }}
          onClick={() => handleEditStore(row)}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "StoreName",
      label: "Store Name",
      description: "Name of the store",
    },
    {
      id: "ChainName",
      label: "Chain Name",
      description: "Name of the chain",
    },
    {
      id: "LocationAddress",
      label: "Address",
      description: "Store address",
    },
    {
      id: "LocationCity",
      label: "City",
      description: "Store city",
    },
    {
      id: "LocationState",
      label: "State",
      description: "Store state",
    },
    {
      id: "LocationZipCode",
      label: "Zip Code",
      description: "Store zip code",
    },
    {
      id: "AccountType",
      label: "Account Type",
      description: "Type of account",
    },
    {
      id: "TerritoryManager",
      label: "Territory Manager",
      description: "Territory manager name",
    },
    {
      id: "DistrictManager",
      label: "District Manager",
      description: "District manager name",
    },
    {
      id: "ReportCategory",
      label: "Report Category",
      description: "Report Category",
    },
    {
      id: "StoreNumber",
      label: "Store Number",
      description: "Store Number",
    },
  ];

  const filterConfigs = [
    {
      name: "store_ids",
      label: "Store IDs",
      type: "custom" as const,
      value: workingFilters.store_ids,
      onChange: (value: string[]) => handleFilterChange("store_ids", value),
      component: (
        <InfiniteScrollDropdown
          field="store_ids"
          label="Store IDs"
          value={workingFilters.store_ids}
          onChange={(value) => handleFilterChange("store_ids", value)}
        />
      ),
    },
    {
      name: "store_names",
      label: "Store Names",
      type: "custom" as const,
      value: workingFilters.store_names,
      onChange: (value: string[]) => handleFilterChange("store_names", value),
      component: (
        <InfiniteScrollDropdown
          field="store_names"
          label="Store Names"
          value={workingFilters.store_names}
          onChange={(value) => handleFilterChange("store_names", value)}
        />
      ),
    },
    {
      name: "chain_names",
      label: "Chain Names",
      type: "custom" as const,
      value: workingFilters.chain_names,
      onChange: (value: string[]) => handleFilterChange("chain_names", value),
      component: (
        <InfiniteScrollDropdown
          field="chain_names"
          label="Chain Names"
          value={workingFilters.chain_names}
          onChange={(value) => handleFilterChange("chain_names", value)}
        />
      ),
    },
    {
      name: "territory_managers",
      label: "Territory Managers",
      type: "custom" as const,
      value: workingFilters.territory_managers,
      onChange: (value: string[]) => handleFilterChange("territory_managers", value),
      component: (
        <InfiniteScrollDropdown
          field="territory_managers"
          label="Territory Managers"
          value={workingFilters.territory_managers}
          onChange={(value) => handleFilterChange("territory_managers", value)}
        />
      ),
    },
    {
      name: "district_managers",
      label: "District Managers",
      type: "custom" as const,
      value: workingFilters.district_managers,
      onChange: (value: string[]) => handleFilterChange("district_managers", value),
      component: (
        <InfiniteScrollDropdown
          field="district_managers"
          label="District Managers"
          value={workingFilters.district_managers}
          onChange={(value) => handleFilterChange("district_managers", value)}
        />
      ),
    },
    {
      name: "store_numbers",
      label: "Store Numbers",
      type: "custom" as const,
      value: workingFilters.store_numbers,
      onChange: (value: string[]) => handleFilterChange("store_numbers", value),
      component: (
        <InfiniteScrollDropdown
          field="store_numbers"
          label="Store Numbers"
          value={workingFilters.store_numbers}
          onChange={(value) => handleFilterChange("store_numbers", value)}
        />
      ),
    },
    {
      name: "account_types",
      label: "Account Types",
      type: "custom" as const,
      value: workingFilters.account_types,
      onChange: (value: string[]) => handleFilterChange("account_types", value),
      component: (
        <InfiniteScrollDropdown
          field="account_types"
          label="Account Types"
          value={workingFilters.account_types}
          onChange={(value) => handleFilterChange("account_types", value)}
        />
      ),
    },
    {
      name: "report_categories",
      label: "Report Categories",
      type: "custom" as const,
      value: workingFilters.report_categories,
      onChange: (value: string[]) => handleFilterChange("report_categories", value),
      component: (
        <InfiniteScrollDropdown
          field="report_categories"
          label="Report Categories"
          value={workingFilters.report_categories}
          onChange={(value) => handleFilterChange("report_categories", value)}
        />
      ),
    },
  ];

  return (
    <div>
      <TopBarLayout
        breadcrumbItems={["Store", "Store Attribution"]}
        onSearchChange={handleSearch}
      />
      <FilterRow
        filters={filterConfigs}
        onReset={handleResetFilters}
        onApply={handleApplyFilters}
      />
      {selectedStore && (
        <EditStoreModal
          open={editModalOpen}
          onClose={() => setEditModalOpen(false)}
          storeData={selectedStore}
          onSuccess={() => {
            toast.success("Store updated successfully!");
            fetchStores(); // Refresh the data
          }}
          onError={(error) => {
            toast.error(`Failed to update store: ${error.message || "Unknown error"}`);
          }}
        />
      )}
      <TableComponent
        columns={columns}
        rows={storeAttributions}
        rowsPerPage={pagination.rowsPerPage}
        page={pagination.page}
        totalCount={storeAttributionTotal}
        onPageChange={handlePageChange}
        showActions={false}
        isLoading={isLoading} // Make sure this only reflects the store loading state
        selectable={true}
        selectedRows={selectedStores}
        onSelectRow={(id) => {
          const selectedIndex = selectedStores.indexOf(id);
          let newSelected: number[] = [];

          if (selectedIndex === -1) {
            newSelected = [...selectedStores, id];
          } else {
            newSelected = selectedStores.filter((storeId) => storeId !== id);
          }

          setSelectedStores(newSelected);
        }}
        onSelectAllRows={(checked) => {
          if (checked) {
            const newSelected = storeAttributions.map((store) => store.NutroStoreID || 0);
            setSelectedStores(newSelected);
          } else {
            setSelectedStores([]);
          }
        }}
        bulkActionButton={actionButtons}
      />
      <ConfirmationModal
        open={deleteConfirmationOpen}
        onClose={() => setDeleteConfirmationOpen(false)}
        onConfirm={handleDeleteConfirmed}
        title="Confirm Status Change"
        message={`Are you sure you want to mark ${selectedStores.length} selected stores as inactive? This will update their status to "Inactive".`}
        confirmText="Mark Inactive"
        cancelText="Cancel"
      />
    </div>
  );
};

export default StoreAttributionPage;
