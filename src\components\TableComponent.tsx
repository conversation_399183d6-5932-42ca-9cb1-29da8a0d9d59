import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  IconButton,
  Tooltip,
  Checkbox,
} from "@mui/material";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import InfoIcon from "@mui/icons-material/Info";

type Column = {
  id: string;
  label: string;
  description?: string;
  format?: (value: any, row: any) => React.ReactNode;
};

type TableComponentProps = {
  columns: Column[];
  rows?: any[];
  onEdit?: (row: any) => void;
  onDelete?: (row: any) => void;
  rowsPerPage?: number;
  page?: number;
  totalCount?: number;
  onPageChange?: (newPage: number, newRowsPerPage: number) => void;
  isLoading?: boolean;
  showActions?: boolean;
  selectable?: boolean;
  selectedRows?: any[];
  onSelectRow?: (row: any) => void;
  onSelectAllRows?: (checked: boolean) => void;
  bulkActionButton?: React.ReactNode;
};

const TableComponent: React.FC<TableComponentProps> = ({
  columns,
  rows = [],
  onEdit = () => {},
  onDelete = () => {},
  rowsPerPage = 20,
  page = 1,
  totalCount = 0,
  onPageChange,
  isLoading = false,
  showActions = true,
  selectable = false,
  selectedRows = [],
  onSelectRow = () => {},
  onSelectAllRows = () => {},
  bulkActionButton,
}) => {
  const totalPages = Math.ceil(totalCount / rowsPerPage);

  const handlePageChange = (newPage: number) => {
    if (onPageChange) {
      onPageChange(newPage, rowsPerPage);
    }
  };

  const handleRowsPerPageChange = (event: any) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    if (onPageChange) {
      onPageChange(1, newRowsPerPage);
    }
  };

  const generatePaginationRange = () => {
    const range = [];
    const siblingCount = 1;

    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i++) {
        range.push(i);
      }
    } else {
      range.push(1);
      if (page > 2 + siblingCount) {
        range.push("...");
      }

      const start = Math.max(2, page - siblingCount);
      const end = Math.min(totalPages - 1, page + siblingCount);

      for (let i = start; i <= end; i++) {
        range.push(i);
      }

      if (page < totalPages - 1 - siblingCount) {
        range.push("...");
      }
      if (totalPages > 1) {
        range.push(totalPages);
      }
    }

    return range;
  };

  const isSelected = (row: any) => {
    return selectedRows.includes(row.NutroStoreID || row.id);
  };

  const handleRowClick = (event: React.MouseEvent<unknown>, row: any) => {
    if (onSelectRow) {
      onSelectRow(row.NutroStoreID || row.id);
    }
  };

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (onSelectAllRows) {
      onSelectAllRows(event.target.checked);
    }
  };

  return (
    <>
      {selectable && bulkActionButton && (
        <Box display="flex" justifyContent="flex-end" mb={1}>
          {bulkActionButton}
        </Box>
      )}

      <TableContainer
        component={Paper}
        sx={{
          mt: 1,
          mx: 1,
          borderRadius: 2,
          boxShadow: 1,
          width: "calc(100% - 16px)",
        }}
      >
        <Table size="small" sx={{ width: "100%", tableLayout: "auto" }}>
          <TableHead>
            <TableRow sx={{ backgroundColor: "rgba(233, 236, 252, 1)" }}>
              {selectable && (
                <TableCell padding="checkbox">
                  <Checkbox
                    color="primary"
                    indeterminate={selectedRows.length > 0 && selectedRows.length < rows.length}
                    checked={rows.length > 0 && selectedRows.length === rows.length}
                    onChange={handleSelectAllClick}
                  />
                </TableCell>
              )}
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align="left"
                  sx={{
                    color: "#000",
                    textTransform: "uppercase",
                    fontWeight: "bold",
                    py: 1,
                    whiteSpace: "nowrap",
                  }}
                >
                  <Box display="flex" alignItems="center" gap={1}>
                    {column.label}
                    {column.description && (
                      <Tooltip title={column.description} arrow>
                        <InfoIcon fontSize="small" sx={{ color: "action.active", opacity: 0.7 }} />
                      </Tooltip>
                    )}
                  </Box>
                </TableCell>
              ))}
              {showActions && (
                <TableCell
                  align="left"
                  sx={{
                    color: "#000",
                    textTransform: "uppercase",
                    fontWeight: "bold",
                    py: 1,
                    whiteSpace: "nowrap",
                  }}
                >
                  Action
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length + (selectable ? 1 : 0) + (showActions ? 1 : 0)}
                  align="center"
                  sx={{ py: 4 }}
                >
                  <CircularProgress size={24} />
                </TableCell>
              </TableRow>
            ) : rows.length > 0 ? (
              rows.map((row, index) => {
                const isItemSelected = isSelected(row);
                return (
                  <TableRow
                    key={index}
                    sx={{
                      backgroundColor:
                        index % 2 === 0 ? "rgba(245, 245, 245, 1)" : "rgba(248, 248, 248, 1)",
                    }}
                    hover
                    role="checkbox"
                    aria-checked={isItemSelected}
                    selected={isItemSelected}
                  >
                    {selectable && (
                      <TableCell padding="checkbox">
                        <Checkbox
                          color="primary"
                          checked={isItemSelected}
                          onClick={(event) => handleRowClick(event, row)}
                        />
                      </TableCell>
                    )}
                    {columns.map((column) => (
                      <TableCell
                        key={column.id}
                        align="left"
                        sx={{ py: 1.2, whiteSpace: "normal" }}
                      >
                        {column.format ? column.format(row[column.id], row) : row[column.id]}
                      </TableCell>
                    ))}
                    {showActions && (
                      <TableCell align="left" sx={{ py: 1.2 }}>
                        <IconButton
                          aria-label="edit"
                          data-testid="edit-button"
                          onClick={() => onEdit(row)}
                          size="small"
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                        <IconButton aria-label="delete" onClick={() => onDelete(row)} size="small">
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length + (selectable ? 1 : 0) + (showActions ? 1 : 0)}
                  align="center"
                  sx={{ py: 4 }}
                >
                  No data available
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {totalCount > 0 && (
        <Box display="flex" justifyContent="space-between" alignItems="center" mt={2} px={2}>
          <Box>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Rows per page</InputLabel>
              <Select value={rowsPerPage} onChange={handleRowsPerPageChange} label="Rows per page">
                <MenuItem value={10}>10</MenuItem>
                <MenuItem value={20}>20</MenuItem>
                <MenuItem value={50}>50</MenuItem>
                <MenuItem value={100}>100</MenuItem>
              </Select>
            </FormControl>
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            <Button
              onClick={() => handlePageChange(page - 1)}
              variant="outlined"
              startIcon={<ArrowBackIosNewIcon fontSize="small" />}
              disabled={page === 1}
              sx={{ mx: 1, border: "none" }}
            >
              Back
            </Button>

            <Box sx={{ mx: 1, display: "flex", gap: 1 }}>
              {generatePaginationRange().map((pageNum, index) => {
                if (pageNum === "...") {
                  return (
                    <Button key={index} disabled sx={{ minWidth: 32 }}>
                      ...
                    </Button>
                  );
                }
                return (
                  <Button
                    key={index}
                    onClick={() => handlePageChange(pageNum as number)}
                    variant={pageNum === page ? "contained" : "outlined"}
                    size="small"
                    sx={{
                      border: "none",
                      borderRadius: pageNum === page ? "50%" : "4px",
                      width: pageNum === page ? "32px" : "auto",
                      height: pageNum === page ? "32px" : "auto",
                      padding: pageNum === page ? "0" : "4px",
                      minWidth: "32px",
                    }}
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </Box>

            <Button
              onClick={() => handlePageChange(page + 1)}
              variant="outlined"
              endIcon={<ArrowForwardIosIcon fontSize="small" />}
              disabled={page === totalPages}
              sx={{ mx: 1, border: "none" }}
            >
              Next
            </Button>
          </Box>

          <Box>
            Showing {(page - 1) * rowsPerPage + 1}-{Math.min(page * rowsPerPage, totalCount)} of{" "}
            {totalCount}
          </Box>
        </Box>
      )}
    </>
  );
};

export default TableComponent;
