import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>po<PERSON>,
  TextField,
  Button,
  Divider,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import { useDispatch } from "react-redux";
import { updateStore } from "../services/action/store.action";
import { StoreUpdateRequest } from "../types/store.types";
import { useAppDispatch, useAppSelector } from "../services/hooks/store.hooks";

interface EditStoreModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  onError?: (error: any) => void;
  storeData: {
    NutroStoreID: number;
    StoreName: string;
    StoreNumber: string;
    ChainName: string;
    AccountType: string;
    LocationAddress: string;
    LocationCity: string;
    LocationState: string;
    LocationCountry: string;
    Phone: string;
    LocationZipCode: string;
    TerritoryManager: string;
    DistrictManager: string;
    StoreStatus: string;
    ReportCategory: string;
  };
}

const EditStoreModal: React.FC<EditStoreModalProps> = ({
  open,
  onClose,
  storeData,
  onSuccess,
  onError,
}) => {
  const dispatch = useDispatch();
  const appDispatch = useAppDispatch();
  const [formData, setFormData] = useState<StoreUpdateRequest>({
    StoreName: "",
    StoreNumber: "",
    ChainName: "",
    AccountType: "",
    LocationAddress: "",
    LocationCity: "",
    LocationState: "",
    LocationCountry: "",
    Phone: "",
    LocationZipCode: "",
    TerritoryManager: "",
    DistrictManager: "",
    StoreStatus: "",
    ReportCategory: "",
  });

  // Country options
  const countryOptions = [
    { value: "", label: "Select Country" },
    { value: "USA", label: "USA" },
    { value: "Canada", label: "Canada" },
    { value: "Others", label: "Others" },
  ];

  // Update formData when storeData changes
  useEffect(() => {
    if (storeData) {
      setFormData({
        StoreName: storeData.StoreName || "",
        StoreNumber: storeData.StoreNumber || "",
        ChainName: storeData.ChainName || "",
        AccountType: storeData.AccountType || "",
        LocationAddress: storeData.LocationAddress || "",
        LocationCity: storeData.LocationCity || "",
        LocationState: storeData.LocationState || "",
        LocationCountry: storeData.LocationCountry || "", // Will be empty if not available
        Phone: storeData.Phone || "",
        LocationZipCode: storeData.LocationZipCode || "",
        TerritoryManager: storeData.TerritoryManager || "",
        DistrictManager: storeData.DistrictManager || "",
        StoreStatus: storeData.StoreStatus || "",
        ReportCategory: storeData.ReportCategory || "",
      });
    }
  }, [storeData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async () => {
    await appDispatch(updateStore({ nutro_store_id: storeData.NutroStoreID, updateData: formData }))
      .unwrap()
      .then(() => {
        onClose();
        onSuccess?.();
      })
      .catch((error) => {
        onError?.(error);
        console.error("Error updating store:", error);
      });
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: { xs: "90%", sm: "80%", md: "70%", lg: "60%" },
          maxHeight: "90vh",
          overflowY: "auto",
          bgcolor: "background.paper",
          boxShadow: 24,
          p: 4,
          borderRadius: 2,
        }}
      >
        <Typography variant="h6" component="h2" gutterBottom>
          Edit Store Attributes
        </Typography>

        {/* Section 1: Basic Info */}
        <Typography variant="subtitle1" sx={{ mt: 2, mb: 1, fontWeight: "bold" }}>
          Basic Information
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Nutro Store ID"
              value={storeData.NutroStoreID}
              disabled
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Store Name"
              name="StoreName"
              value={formData.StoreName}
              onChange={handleChange}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Store Number"
              name="StoreNumber"
              value={formData.StoreNumber}
              onChange={handleChange}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Chain Name"
              name="ChainName"
              value={formData.ChainName}
              onChange={handleChange}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Report Category"
              name="ReportCategory"
              value={formData.ReportCategory}
              onChange={handleChange}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Account Type</InputLabel>
              <Select
                name="AccountType"
                value={formData.AccountType}
                onChange={handleSelectChange}
                label="Account Type"
              >
                <MenuItem value="National">National</MenuItem>
                <MenuItem value="Regional">Regional</MenuItem>
                <MenuItem value="Independent">Independent</MenuItem>
                <MenuItem value="Other">Other</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Divider sx={{ my: 3 }} />

        {/* Section 2: Address Info */}
        <Typography variant="subtitle1" sx={{ mt: 2, mb: 1, fontWeight: "bold" }}>
          Address Information
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Street Address"
              name="LocationAddress"
              value={formData.LocationAddress}
              onChange={handleChange}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="City"
              name="LocationCity"
              value={formData.LocationCity}
              onChange={handleChange}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="State"
              name="LocationState"
              value={formData.LocationState}
              onChange={handleChange}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Country</InputLabel>
              <Select
                name="LocationCountry"
                value={formData.LocationCountry}
                onChange={handleSelectChange}
                label="Country"
              >
                {countryOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Phone"
              name="Phone"
              value={formData.Phone}
              onChange={handleChange}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Zip Code"
              name="LocationZipCode"
              value={formData.LocationZipCode}
              onChange={handleChange}
              margin="normal"
            />
          </Grid>
        </Grid>

        <Divider sx={{ my: 3 }} />

        {/* Section 3: Management Info */}
        <Typography variant="subtitle1" sx={{ mt: 2, mb: 1, fontWeight: "bold" }}>
          Management Information
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Territory Manager"
              name="TerritoryManager"
              value={formData.TerritoryManager}
              onChange={handleChange}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="District Manager"
              name="DistrictManager"
              value={formData.DistrictManager}
              onChange={handleChange}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth margin="normal">
              <InputLabel>Status</InputLabel>
              <Select
                name="StoreStatus"
                value={formData.StoreStatus}
                onChange={handleSelectChange}
                label="Status"
              >
                <MenuItem value="A">Active Account</MenuItem>
                <MenuItem value="D">Previous Account Inactive</MenuItem>
                <MenuItem value="O">Out of Business</MenuItem>
                <MenuItem value="P">Prospect Account</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Box sx={{ mt: 4, display: "flex", justifyContent: "flex-end", gap: 2 }}>
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="contained" color="primary" onClick={handleSubmit}>
            Save Changes
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default EditStoreModal;
