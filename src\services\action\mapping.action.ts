import { createAsyncThunk } from '@reduxjs/toolkit';
import { mappingService, UnknownSearchParams, MappedSearchParams, UnknownSearchResponse, MappedSearchResponse } from '../api/mapping.service';

// Async thunk for searching unknown mappings
export const searchUnknownMappings = createAsyncThunk<
  UnknownSearchResponse,
  UnknownSearchParams,
  { rejectValue: string }
>(
  'mapping/searchUnknownMappings',
  async (params, { rejectWithValue }) => {
    try {
      const response = await mappingService.searchUnknowns(params);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to search unknown mappings');
    }
  }
);

// Async thunk for searching mapped items
export const searchMappedItems = createAsyncThunk<
  MappedSearchResponse,
  MappedSearchParams,
  { rejectValue: string }
>(
  'mapping/searchMappedItems',
  async (params, { rejectWithValue }) => {
    try {
      const response = await mappingService.searchMapped(params);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to search mapped items');
    }
  }
);
