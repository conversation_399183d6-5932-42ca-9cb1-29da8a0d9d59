import client from "../../axiosConfig";

const AttributeService = {
  // Get Attribute List Items
  async getAttributeListItems(
    limit: number,
    page: number,
    list_name: string,
    search_query: string = "",
  ) {
    const response = await client.get("/attribute_list/", {
      params: { limit, page, list_name, search_query },
    });
    return {
      attributes: response.data.attributeList,
      totalCount: response.data.totalCount,
    };
  },
  // Get All Unique List Names
  getAllListNames() {
    return client.get("/attribute_list/list-names/");
  },
  // Create Attribute List Item
  createAttributeListItem(data: any) {
    return client.post("/attribute_list/", data);
  },

  // Update Attribute List Item
  updateAttributeListItem(id: number, data: any) {
    return client.put(`/attribute_list/${id}`, data);
  },

  // Patch Attribute List Item
  patchAttributeListItem(id: number, data: any) {
    return client.patch(`/attribute_list/${id}`, data);
  },

  // Delete Attribute List Item
  deleteAttributeListItem(id: number) {
    return client.delete(`/attribute_list/${id}`);
  },
};

export default AttributeService;
